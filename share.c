#include "share.h"

// LOGOUT FUNCTION
int logout() {
    printf("Logging out...\n");
    Sleep(1000); // sleep for 1000 milliseconds = 1 second
    printf("You have been logged out successfully!\n");
    return -1;
}

// CONFIRMATION FUNCTION
int confirmation(const char *prompt) {
    int confirm = get_integer_input(prompt);
    switch (confirm) {
        case 1: // YES
            return 1;
        case 2: // NO
            return 2;
        default:
            printf("\nInvalid input! Please enter 1 for 'Yes' or 2 for 'No'.\n");
            return 0;
    }

}

// EXIT FUNCTION
int ask_exit() {
    int exit = get_integer_input("\nExit? (1. Yes / 2. No): ");
    switch (exit) {
        case YES:
            return YES;
        case NO:
            return NO;
        default:
            printf("\nInvalid input! Please enter 1 for 'Yes' or 2 for 'No'.");
            return 0;
    }
}

// GET INTEGER INPUT FUNCTION
int get_integer_input(const char *prompt) {
    char buffer[100];
    int value;
    char extra;
    
    while (1) {
        printf("%s", prompt);
        fgets(buffer, sizeof(buffer), stdin);
        
        if (sscanf(buffer, "%d %c", &value, &extra) == 1) {
            return value;
        }
        printf("Invalid input. Please enter a valid choice.\n");
    }
}

bool get_float_input(const char *str) {
    bool has_decimal = false;
    bool has_digit = false;

    if (*str == '\0') return false;

    while (*str) {
        if (isdigit(*str)) {
            has_digit = true;
        } else if (*str == '.') {
            if (has_decimal) return false;
            has_decimal = true;
        } else {
            return false;
        }
        str++;
    }

    return has_digit;
}

void clear_input_buffer() {
    int c;
    while ((c = getchar()) != '\n' && c != EOF);
}
