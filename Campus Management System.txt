Campus Management System

Task: Develop a comprehensive Campus Food Delivery Management System using C programming language. This console-based application will manage students, campus restaurants, delivery personnel, and orders through a menu-driven interface with file-based data persistence.

Key Features:

•	Student Management Features
o	Student registration with validation
o	Student login authentication
o	Profile management (view/add/update/delete personal information)
o	Order placement with menu selection
o	Order history tracking
o	Account balance management

•	Restaurant Management Features
o	Restaurant registration and login
o	Menu item management (view/add/update/delete items)
o	Order queue management
o	Daily sales reporting
o	Inventory tracking with low-stock alerts

•	Delivery Management Features
o	Delivery personnel registration and login
o	Order assignment system
o	Delivery status updates (view/add/update/delete)
o	Earnings calculation and tracking
o	Performance statistics

•	Administrative Features
o	System administrator login
o	User management (view all users, activate/deactivate accounts, add/update/delete user details)
o	System-wide reporting and analytics
o	Data backup and restore functionality
o	System configuration management

Restrictions:
1. The program should use symbolic constants where appropriate. Validations need to be included to ensure the accuracy of the system. State any assumptions that you make under each function. 
2. Store all data in a text file. 
3. Use control structures, functions, array, pointers, structures, unions and files in your program.
4. The program must embrace modular programming technique and should be menu-driven.
5. Functions of similar operations can be grouped (or kept alone) and stored as separate C files.
6. Header files are to be stored separately as .h files.
7. Include any extra features which you may feel relevant and that add value to the system. 
8. There should be no need for graphics (user interface) in your program
9. Include the good programming practice such as comments, variable naming conventions and indentation.
10. Use portable ANSI C programming language to implement the solution.
11. Use of any other language like C++/Java and etc. is not allowed.
12. Global variable is not allowed.